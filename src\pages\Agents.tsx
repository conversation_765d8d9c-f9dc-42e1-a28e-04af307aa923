import React from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Globe, Settings, Plus } from 'lucide-react';
import { useChat, useAgents } from '../lib/store';

export function AgentsPage() {
  const [selectedAgent, setSelectedAgent] = React.useState<string | null>(null);
  const [isSettingsOpen, setIsSettingsOpen] = React.useState<string | null>(null);
  const { sendMessage } = useChat();
  const { agents } = useAgents();

  const handleAgentClick = (agentId: string) => {
    setSelectedAgent(agentId);
    const agent = agents.find(a => a.id === agentId);
    if (agent) {
      sendMessage(`Starting conversation with ${agent.name}...`);
    }
  };

  const handleCreateAgent = () => {
    // TODO: Implement agent creation
    console.log('Create new agent');
  };

  return (
    <div className="p-10">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">Agents</h1>
        <button
          onClick={handleCreateAgent}
          className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
        >
          <Plus size={20} />
          <span>Create Agent</span>
        </button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {agents.map((agent, index) => (
          <div
            key={agent.id}
            onClick={() => handleAgentClick(agent.id)}
            className="bg-[#141414] rounded-2xl p-6 border border-white/5 hover:border-white/10 transition-all duration-300 group cursor-pointer relative"
          >
            <button
              onClick={(e) => {
                e.stopPropagation();
                setIsSettingsOpen(agent.id);
              }}
              className="absolute top-4 right-4 p-2 rounded-lg hover:bg-white/10 transition-colors"
            >
              <Settings size={16} className="text-gray-400 hover:text-white" />
            </button>
            <div className="flex items-start space-x-4">
              <div className="relative">
                <img
                  src={agent.avatar}
                  alt={agent.name}
                  className="w-16 h-16 rounded-xl object-cover"
                />
                <div className="absolute -bottom-2 -right-2 w-8 h-8 rounded-lg bg-[#1A1A1A] border border-white/5 flex items-center justify-center">
                  <agent.icon size={16} className="text-white/80" />
                </div>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold mb-1 group-hover:text-white transition-colors">
                  {agent.name}
                </h3>
                <p className="text-sm text-gray-400 mb-3">
                  {agent.description}
                </p>
                <div className="flex items-center space-x-4 text-xs">
                  <span className="px-2 py-1 rounded-md bg-white/5 text-white/80">
                    {agent.specialty}
                  </span>
                  <span className="text-gray-500">
                    {agent.experience}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}