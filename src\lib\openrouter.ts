import { type Message } from './store';

export interface ChatResponse {
  choices: {
    message: Message;
  }[];
}

export async function chat(messages: Message[], apiKey: string, model: string): Promise<ChatResponse> {
  if (!apiKey) {
    throw new Error('API key is required. Please add your API key in Settings.');
  }

  const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`,
      'HTTP-Referer': window.location.origin,
      'X-Title': 'AG3NT X'
    },
    body: JSON.stringify({
      model,
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }))
    })
  });

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(`OpenRouter API Error:\n\n${error.error || error.message || 'Unknown error'}`);
  }

  return response.json();
}