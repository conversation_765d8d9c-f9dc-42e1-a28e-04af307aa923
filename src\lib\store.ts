import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { chat, type ChatResponse } from './openrouter';
import { Brain, Code, Globe, Bot, User } from 'lucide-react';

const agents = [
  {
    id: 'user',
    name: 'User Agent',
    description: 'Your personal agent that coordinates with other agents',
    avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&auto=format&fit=crop&q=80',
    icon: User,
    specialty: 'Coordination',
    experience: 'Personal Assistant',
    config: {
      temperature: 0.7,
      model: 'anthropic/claude-3-sonnet'
    }
  },
  {
    id: 'research',
    name: 'Research Assistant',
    description: 'Helps with research, summarization, and analysis',
    avatar: 'https://images.unsplash.com/photo-1639322537228-f710d846310a?w=400&auto=format&fit=crop&q=80',
    icon: Brain,
    specialty: 'Research & Analysis',
    experience: '1000+ tasks completed',
    config: {
      temperature: 0.7,
      model: 'anthropic/claude-3-sonnet'
    }
  },
  {
    id: 'code',
    name: '<PERSON> Expert',
    description: 'Assists with coding, debugging, and technical solutions',
    avatar: 'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=400&auto=format&fit=crop&q=80',
    icon: Code,
    specialty: 'Software Development',
    experience: '2000+ commits',
    config: {
      temperature: 0.3,
      model: 'anthropic/claude-3-sonnet'
    }
  },
  {
    id: 'web',
    name: 'Web Navigator',
    description: 'Browses and analyzes web content in real-time',
    avatar: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&auto=format&fit=crop&q=80',
    icon: Globe,
    specialty: 'Web Analysis',
    experience: '5000+ pages processed',
    config: {
      temperature: 0.5,
      model: 'anthropic/claude-3-sonnet'
    }
  },
  {
    id: 'general',
    name: 'General Assistant',
    description: 'Versatile AI helper for various tasks and queries',
    avatar: 'https://images.unsplash.com/photo-1620712943543-bcc4688e7485?w=400&auto=format&fit=crop&q=80',
    icon: Bot,
    specialty: 'General Assistance',
    experience: '10000+ conversations',
    config: {
      temperature: 0.7,
      model: 'anthropic/claude-3-sonnet'
    }
  }
];

export interface Message {
  role: 'user' | 'assistant';
  content: string | {
    type: string;
    text?: string;
    image_url?: {
      url: string;
    };
  }[];
}

interface SettingsState {
  apiKey: string;
  model: string;
  showUserAgent: boolean;
  setApiKey: (key: string) => void;
  setModel: (model: string) => void;
  setShowUserAgent: (show: boolean) => void;
}

interface ChatState {
  messages: Message[];
  currentAgent: string | null;
  sendMessage: (content: string) => Promise<void>;
  setCurrentAgent: (agentId: string | null) => void;
}

interface AgentsState {
  agents: typeof agents;
}

export const useSettings = create<SettingsState>()(
  persist(
    (set) => ({
      apiKey: '',
      model: 'anthropic/claude-3-sonnet',
      showUserAgent: true,
      setApiKey: (apiKey) => set({ apiKey }),
      setModel: (model) => set({ model }),
      setShowUserAgent: (show) => set({ showUserAgent: show }),
    }),
    {
      name: 'settings-storage',
    }
  )
);

export const useChat = create<ChatState>((set, get) => ({
  messages: [],
  currentAgent: null,
  setCurrentAgent: (agentId) => set({ currentAgent: agentId }),
  sendMessage: async (content: string) => {
    const settings = useSettings.getState();
    const messages = [...get().messages, { role: 'user', content }];
    
    set({ messages });
    
    try {
      if (!settings.apiKey) {
        throw new Error('API key is required. Please add your API key in Settings.');
      }

      const response: ChatResponse = await chat(messages, settings.apiKey, settings.model);
      
      if (!response?.choices?.[0]?.message) {
        throw new Error('Invalid response from API');
      }

      const assistantMessage = response.choices[0].message;
      set({ messages: [...messages, assistantMessage] });
    } catch (error) {
      // Remove the failed message
      set({ messages: messages.slice(0, -1) });
      throw error instanceof Error ? error : new Error('An unknown error occurred');
    }
  }
}));

export const useAgents = create<AgentsState>(() => ({
  agents: agents
}));